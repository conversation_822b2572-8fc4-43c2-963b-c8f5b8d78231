# SemiPRO Semiconductor Simulator - Git Ignore File

# Build directories and compiled outputs
build/
Build/
BUILD/
out/
dist/
bin/
lib/
libs/

# CMake generated files
CMakeCache.txt
CMakeFiles/
cmake_install.cmake
CTestTestfile.cmake
Makefile
*.cmake
!CMakeLists.txt

# Compiled binaries and libraries
*.exe
*.out
*.app
*.dll
*.dylib
*.a
*.lib

# Specific executables (but keep source code)
simulator
tests
example_*
tutorial_*
working_*
*_example
*_demo

# Object files
*.o
*.obj
*.lo
*.slo
*.ko

# Debug files
*.dSYM/
*.su
*.idb
*.pdb

# Temporary files
*.tmp
*.temp
*~
*.swp
*.swo
.DS_Store
Thumbs.db

# IDE and editor files
.vscode/
.idea/
*.vcxproj*
*.sln
*.suo
*.user
*.userosscache
*.sln.docstates
*.code-workspace

# Qt Creator
CMakeLists.txt.user*

# Xcode
*.xcodeproj/
*.xcworkspace/

# Logs and runtime files
logs/
*.log
*.log.*
core.*
!src/cpp/core/
vgcore.*

# Configuration files (keep templates and examples)
config/*.yaml
config/*.yml
config/*.json
!config/*.template.*
!config/example_*

# Python cache and bytecode (but keep source .py files)
__pycache__/
*.pyc
*.pyo
*.pyd
*$py.class

# Python compiled extensions (but keep source .py files)
src/**/__pycache__/
src/**/*.pyc
src/**/*.pyo
.Python
env/
venv/
ENV/
env.bak/
venv.bak/
.pytest_cache/

# Python distribution / packaging
develop-eggs/
downloads/
eggs/
.eggs/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Jupyter Notebook
.ipynb_checkpoints

# pyenv
.python-version

# Vulkan and graphics compiled shaders
*.spv
shaders/compiled/
vulkan_debug.txt

# Documentation build outputs
docs/_build/
docs/build/
site/

# Test results and coverage
*.gcov
*.gcno
*.gcda
coverage/
htmlcov/
.coverage
.coverage.*
coverage.xml
*.cover
.hypothesis/

# Profiling data
*.prof
*.perf
callgrind.out.*
massif.out.*
gmon.out

# Memory debugging
valgrind-*.log
*.memcheck

# Backup files
*.bak
*.backup
*.orig

# Archive files
*.tar
*.tar.gz
*.tar.bz2
*.tar.xz
*.zip
*.rar
*.7z

# Package manager files
Pipfile.lock
poetry.lock
yarn.lock
package-lock.json

# Environment variables
.env
.env.local
.env.*.local

# Database files
*.db
*.sqlite
*.sqlite3

# Simulation output files and data
results/
output/
data/
*.dat
*.hdf5
*.h5

# SemiPRO specific output artifacts
output/visualizations/
output/reports/
output/test_*
config/test_*.json

# Simulation artifacts and generated images
dopant_profile.dat
dopant_profile_visualization.png
wafer_3d_visualization.png
mosfet_final.png
temperature_field.png
stress_analysis.png
*_visualization.png
*_plot.png
*_chart.png
*_graph.png

# Generated reports and documentation
reports/
analysis/
benchmarks/
validation_results/
performance_reports/
scientific_validation/

# Large data files
*.bin
*.raw
*.dump

# Temporary simulation files
sim_temp_*
wafer_*.tmp
process_*.cache

# Generated documentation
doxygen/
sphinx/_build/

# Package managers
conanfile.txt
conaninfo.txt
conanbuildinfo.*
conan.lock
vcpkg_installed/
_deps/

# Build tools
.ccache/
.ninja_deps
.ninja_log
.clangd/
compile_commands.json

# Static analysis
cppcheck-*.xml
scan-build-*/

# Sanitizer outputs
*.asan
*.tsan
*.msan
*.ubsan

# Benchmark results
benchmark_results/
*.benchmark

# Machine learning models
*.model
*.pkl
*.joblib

# Simulation checkpoints
checkpoints/
*.checkpoint

# Generated media files (but keep source images in docs/)
*.mp4
*.avi
!docs/**/*.png
!docs/**/*.jpg
!docs/**/*.gif
!README*.png
!README*.jpg

# CAD files (large)
*.gds
*.gdsii
*.oasis

# SPICE simulation outputs
*.sp
*.cir
*.lis

# Custom application data
.semipro/
user_data/
preferences.ini

# Cython compiled files (but keep .pyx source files)
src/cython/*.c
src/cython/*.cpp
src/cython/*.so
src/cython/*.pyd
src/cython/*.html
!src/cython/*.pyx
!src/cython/*.pxd
!src/cython/setup.py
!src/cython/build_single.py

# Large downloaded dependencies
Cython-*/
eigen-*/
yaml-cpp-*/
glfw-*/
vulkan-*/

# Downloaded dependencies and build artifacts
*.whl
pip-wheel-metadata/
share/python-wheels/

# Test artifacts
test_results/
coverage_reports/
.tox/
.nox/
nosetests.xml

# Additional Python artifacts
*.manifest
*.spec
pip-log.txt
pip-delete-this-directory.txt
.cache/

# Documentation artifacts (but keep source docs)
docs/build/
docs/_build/
!docs/**/*.md
!docs/**/*.rst

# IDE specific
.spyderproject
.spyproject
.ropeproject
*.sublime-*

# Package management
__pypackages__/
.pipenv/

# OS specific
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db
*.lnk
*.url
.fuse_hidden*
.directory
.Trash-*
.nfs*
.AppleDouble
.LSOverride
Icon
.DocumentRevisions-V100
.fseventsd
.TemporaryItems
.VolumeIcon.icns
.com.apple.timemachine.donotpresent
.AppleDB
.AppleDesktop
Network Trash Folder
Temporary Items
.apdisk

# IMPORTANT: Keep all source code files
# This ensures .cpp, .hpp, .c, .h, .py, .pyx files are included
!src/**/*.cpp
!src/**/*.hpp
!src/**/*.c
!src/**/*.h
!src/**/*.py
!src/**/*.pyx
!src/**/*.pxd
!*.cpp
!*.hpp
!*.c
!*.h
!*.py
!*.pyx
!*.pxd
!CMakeLists.txt
!*.md
!*.rst
!*.txt
!*.yaml
!*.yml
!*.json
!*.frag
!*.vert
!*.glsl
