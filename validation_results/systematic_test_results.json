{"timestamp": "2025-07-10 17:54:42", "results": {"oxidation": {"success": true, "execution_time": 0.003292083740234375, "stdout": "SemiPRO C++ Simulator starting...\n\u001b[32m[2025-07-10 17:54:42.697] [INFO] [GEN] [T:132539] [ConfigManager] Configuration Manager initialized (in ConfigManager at config_manager.cpp:257)\u001b[0m\n\u001b[32m[2025-07-10 17:54:42.698] [INFO] [MEM] [T:132539] [MemoryManager] Enhanced Memory Manager initialized (in MemoryManager at memory_manager.cpp:13)\u001b[0m\n\u001b[32m[2025-07-10 17:54:42.698] [INFO] [GEN] [T:132539] Advanced systems initialized (logging, config, memory)\u001b[0m\nDEBUG: simulateProcessAsync called for wafer: main_wafer, operation: oxidation\nDEBUG: Inside async lambda, about to call executeProcess\n\u001b[32m[2025-07-10 17:54:42.698] [INFO] [SIM] [T:132539] [SimulationEngine] Starting process: oxidation on wafer: main_wafer (Memory usage: 0 bytes) (in executeProcess at simulation_engine.cpp:229)\u001b[0m\n\u001b[32m[2025-07-10 17:54:42.698] [INFO] [PHYS] [T:132539] [EnhancedOxidation] Enhanced Oxidation Physics initialized (in EnhancedOxidationPhysics at enhanced_oxidation.cpp:13)\u001b[0m\n\u001b[32m[2025-07-10 17:54:42.698] [INFO] [PHYS] [T:132539] [EnhancedOxidation] Starting enhanced oxidation: 1000.000000°C, 1.000000h, Dry O2 (in simulateOxidation at simulation_engine.cpp:446)\u001b[0m\n\u001b[32m[2025-07-10 17:54:42.698] [INFO] [PHYS] [T:132539] [EnhancedOxidation] Enhanced oxidation completed: 0.557734 μm oxide grown (in simulateOxidation at enhanced_oxidation.cpp:86)\u001b[0m\nDEBUG: executeProcess returned: true\n\u001b[32m[2025-07-10 17:54:42.698] [INFO] [PHYS] [T:132539] [EnhancedOxidation] Enhanced oxidation completed: 0.557734 μm oxide grown, Growth rate: 0.009137 μm/h, Regime: Thick oxide, Stress: 96482.665162 MPa, Quality score: 0.000000 (in simulateOxidation at simulation_engine.cpp:456)\u001b[0m\n\u001b[32m[2025-07-10 17:54:42.698] [INFO] [SIM] [T:132539] Process completed successfully: oxidation (in executeProcess at simulation_engine.cpp:274)\u001b[0m\n\u001b[32m[2025-07-10 17:54:42.699] [INFO] [MEM] [T:132539] [MemoryManager] Starting memory cleanup (in cleanup at memory_manager.cpp:235)\u001b[0m\n\u001b[32m[2025-07-10 17:54:42.699] [INFO] [MEM] [T:132539] [MemoryManager] Memory cleanup complete (in cleanup at memory_manager.cpp:247)\u001b[0m\n\u001b[32m[2025-07-10 17:54:42.699] [INFO] [MEM] [T:132539] [MemoryManager] Memory Manager destroyed (in ~MemoryManager at memory_manager.cpp:28)\u001b[0m\n", "stderr": "", "output_files": 3}, "doping": {"success": true, "execution_time": 0.0036275386810302734, "stdout": "SemiPRO C++ Simulator starting...\n\u001b[32m[2025-07-10 17:54:42.702] [INFO] [GEN] [T:130295] [Config<PERSON>anager] Configuration Manager initialized (in ConfigManager at config_manager.cpp:257)\u001b[0m\n\u001b[32m[2025-07-10 17:54:42.702] [INFO] [MEM] [T:130295] [MemoryManager] Enhanced Memory Manager initialized (in MemoryManager at memory_manager.cpp:13)\u001b[0m\n\u001b[32m[2025-07-10 17:54:42.702] [INFO] [GEN] [T:130295] Advanced systems initialized (logging, config, memory)\u001b[0m\nStarting ion implantation simulation...\nDEBUG: About to call simulateProcessAsync for doping\nDEBUG: simulateProcessAsync called for wafer: main_wafer, operation: doping\nDEBUG: simulateProcessAsync returned, getting result...\nDEBUG: Inside async lambda, about to call executeProcess\nDEBUG: simulateIonImplantation function entered\nDEBUG: Inside try block, extracting parameters\nDEBUG: Ion implantation calculations:\n  energy=\u001b[32m[2025-07-10 17:54:42.702] [INFO] [SIM] [T:130295] [SimulationEngine] Starting process: doping on wafer: main_wafer (Memory usage: 0 bytes) (in executeProcess at simulation_engine.cpp:229)\u001b[0m\n50 keV\n  dose=1e+14 cm^-2\n  mass=11 amu\n  atomic_number=5\n  epsilon=2.34282\n  reduced_range=0.07462\n  range=2.66259e-05 μm\n  straggling=1.11829e-05 μm\nDEBUG: executeProcess returned: true\n\u001b[32m[2025-07-10 17:54:42.703] [INFO] [SIM] [T:130295] Process completed successfully: doping (in executeProcess at simulation_engine.cpp:274)\u001b[0m\nDEBUG: Result obtained: true\nIon implantation completed: SUCCESS\n\u001b[32m[2025-07-10 17:54:42.703] [INFO] [MEM] [T:130295] [MemoryManager] Starting memory cleanup (in cleanup at memory_manager.cpp:235)\u001b[0m\n\u001b[32m[2025-07-10 17:54:42.703] [INFO] [MEM] [T:130295] [MemoryManager] Memory cleanup complete (in cleanup at memory_manager.cpp:247)\u001b[0m\n\u001b[32m[2025-07-10 17:54:42.703] [INFO] [MEM] [T:130295] [MemoryManager] Memory Manager destroyed (in ~MemoryManager at memory_manager.cpp:28)\u001b[0m\n", "stderr": "", "output_files": 3}, "deposition": {"success": true, "execution_time": 0.0035452842712402344, "stdout": "SemiPRO C++ Simulator starting...\n\u001b[32m[2025-07-10 17:54:42.706] [INFO] [GEN] [T:139928] [ConfigManager] Configuration Manager initialized (in ConfigManager at config_manager.cpp:257)\u001b[0m\n\u001b[32m[2025-07-10 17:54:42.706] [INFO] [MEM] [T:139928] [MemoryManager] Enhanced Memory Manager initialized (in MemoryManager at memory_manager.cpp:13)\u001b[0m\n\u001b[32m[2025-07-10 17:54:42.706] [INFO] [GEN] [T:139928] Advanced systems initialized (logging, config, memory)\u001b[0m\nStarting deposition simulation...\n  Material: aluminum\n  Thickness: 0.1 μm\n  Temperature: 300°C\nDEBUG: About to call simulateProcessAsync for deposition\nDEBUG: simulateProcessAsync called for wafer: main_wafer, operation: deposition\nDEBUG: simulateProcessAsync returned, getting result...\nDEBUG: Inside async lambda, about to call executeProcess\nDEBUG: Deposition calculations:\n  material=aluminum\n  thickness=0.1 μm\n  temperature=300°C\n  rate=0.1 μm/min\n  temp_factor=0.222393\n  effective_rate=0.0222393 μm/min\n  deposition_time=4.49654 minutes\n\u001b[32m[2025-07-10 17:54:42.706] [INFO] [SIM] [T:139928] [SimulationEngine] Starting process: deposition on wafer: main_wafer (Memory usage: 0 bytes) (in executeProcess at simulation_engine.cpp:229)\u001b[0m\nDEBUG: executeProcess returned: true\n\u001b[32m[2025-07-10 17:54:42.707] [INFO] [SIM] [T:139928] Process completed successfully: deposition (in executeProcess at simulation_engine.cpp:274)\u001b[0m\nDEBUG: Result obtained: true\nDeposition completed: SUCCESS\n\u001b[32m[2025-07-10 17:54:42.707] [INFO] [MEM] [T:139928] [MemoryManager] Starting memory cleanup (in cleanup at memory_manager.cpp:235)\u001b[0m\n\u001b[32m[2025-07-10 17:54:42.707] [INFO] [MEM] [T:139928] [MemoryManager] Memory cleanup complete (in cleanup at memory_manager.cpp:247)\u001b[0m\n\u001b[32m[2025-07-10 17:54:42.707] [INFO] [MEM] [T:139928] [MemoryManager] Memory Manager destroyed (in ~MemoryManager at memory_manager.cpp:28)\u001b[0m\n", "stderr": "", "output_files": 3}, "etching": {"success": true, "execution_time": 0.0032470226287841797, "stdout": "SemiPRO C++ Simulator starting...\n\u001b[32m[2025-07-10 17:54:42.710] [INFO] [GEN] [T:138059] [ConfigManager] Configuration Manager initialized (in ConfigManager at config_manager.cpp:257)\u001b[0m\n\u001b[32m[2025-07-10 17:54:42.710] [INFO] [MEM] [T:138059] [MemoryManager] Enhanced Memory Manager initialized (in MemoryManager at memory_manager.cpp:13)\u001b[0m\n\u001b[32m[2025-07-10 17:54:42.710] [INFO] [GEN] [T:138059] Advanced systems initialized (logging, config, memory)\u001b[0m\nDEBUG: simulateProcessAsync called for wafer: main_wafer, operation: etching\nDEBUG: Inside async lambda, about to call executeProcess\n\u001b[32m[2025-07-10 17:54:42.710] [INFO] [SIM] [T:138059] [SimulationEngine] Starting process: etching on wafer: main_wafer (Memory usage: 0 bytes) (in executeProcess at simulation_engine.cpp:229)\u001b[0m\nDEBUG: executeProcess returned: true\n\u001b[32m[2025-07-10 17:54:42.711] [INFO] [SIM] [T:138059] Process completed successfully: etching (in executeProcess at simulation_engine.cpp:274)\u001b[0m\n\u001b[32m[2025-07-10 17:54:42.711] [INFO] [MEM] [T:138059] [MemoryManager] Starting memory cleanup (in cleanup at memory_manager.cpp:235)\u001b[0m\n\u001b[32m[2025-07-10 17:54:42.711] [INFO] [MEM] [T:138059] [MemoryManager] Memory cleanup complete (in cleanup at memory_manager.cpp:247)\u001b[0m\n\u001b[32m[2025-07-10 17:54:42.711] [INFO] [MEM] [T:138059] [MemoryManager] Memory Manager destroyed (in ~MemoryManager at memory_manager.cpp:28)\u001b[0m\n", "stderr": "", "output_files": 3}}, "analysis": {"total": 4, "successful": 4, "success_rate": 1.0, "working_modules": ["oxidation", "doping", "deposition", "etching"], "timeout_modules": [], "config_error_modules": []}}