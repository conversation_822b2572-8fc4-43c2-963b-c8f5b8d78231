{"timestamp": "2025-07-10 18:46:53", "results": {"oxidation": {"success": true, "execution_time": 0.0034339427947998047, "stdout": "SemiPRO C++ Simulator starting...\n\u001b[32m[2025-07-10 18:46:53.968] [INFO] [GEN] [T:126739] [ConfigManager] Configuration Manager initialized (in ConfigManager at config_manager.cpp:257)\u001b[0m\n\u001b[32m[2025-07-10 18:46:53.968] [INFO] [MEM] [T:126739] [MemoryManager] Enhanced Memory Manager initialized (in MemoryManager at memory_manager.cpp:13)\u001b[0m\n\u001b[32m[2025-07-10 18:46:53.968] [INFO] [GEN] [T:126739] Advanced systems initialized (logging, config, memory)\u001b[0m\nDEBUG: simulateProcessAsync called for wafer: main_wafer, operation: oxidation\nDEBUG: Inside async lambda, about to call executeProcess\n\u001b[32m[2025-07-10 18:46:53.969] [INFO] [SIM] [T:126739] [SimulationEngine] Starting process: oxidation on wafer: main_wafer (Memory usage: 0 bytes) (in executeProcess at simulation_engine.cpp:232)\u001b[0m\n\u001b[32m[2025-07-10 18:46:53.969] [INFO] [PHYS] [T:126739] [EnhancedOxidation] Enhanced Oxidation Physics initialized (in EnhancedOxidationPhysics at enhanced_oxidation.cpp:13)\u001b[0m\n\u001b[32m[2025-07-10 18:46:53.969] [INFO] [PHYS] [T:126739] [EnhancedOxidation] Starting enhanced oxidation: 1000.000000°C, 1.000000h, Dry O2 (in simulateOxidation at simulation_engine.cpp:449)\u001b[0m\nDEBUG: executeProcess returned: true\n\u001b[32m[2025-07-10 18:46:53.969] [INFO] [PHYS] [T:126739] [EnhancedOxidation] Enhanced oxidation completed: 0.557734 μm oxide grown (in simulateOxidation at enhanced_oxidation.cpp:86)\u001b[0m\n\u001b[32m[2025-07-10 18:46:53.969] [INFO] [PHYS] [T:126739] [EnhancedOxidation] Enhanced oxidation completed: 0.557734 μm oxide grown, Growth rate: 0.009137 μm/h, Regime: Thick oxide, Stress: 96482.665162 MPa, Quality score: 0.000000 (in simulateOxidation at simulation_engine.cpp:459)\u001b[0m\n\u001b[32m[2025-07-10 18:46:53.969] [INFO] [SIM] [T:126739] Process completed successfully: oxidation (in executeProcess at simulation_engine.cpp:277)\u001b[0m\n\u001b[32m[2025-07-10 18:46:53.969] [INFO] [MEM] [T:126739] [MemoryManager] Starting memory cleanup (in cleanup at memory_manager.cpp:235)\u001b[0m\n\u001b[32m[2025-07-10 18:46:53.969] [INFO] [MEM] [T:126739] [MemoryManager] Memory cleanup complete (in cleanup at memory_manager.cpp:247)\u001b[0m\n\u001b[32m[2025-07-10 18:46:53.969] [INFO] [MEM] [T:126739] [MemoryManager] Memory Manager destroyed (in ~MemoryManager at memory_manager.cpp:28)\u001b[0m\n", "stderr": "", "output_files": 3}, "doping": {"success": true, "execution_time": 0.00684809684753418, "stdout": "SemiPRO C++ Simulator starting...\n\u001b[32m[2025-07-10 18:46:53.973] [INFO] [GEN] [T:134830] [ConfigManager] Configuration Manager initialized (in ConfigManager at config_manager.cpp:257)\u001b[0m\n\u001b[32m[2025-07-10 18:46:53.973] [INFO] [MEM] [T:134830] [MemoryManager] Enhanced Memory Manager initialized (in MemoryManager at memory_manager.cpp:13)\u001b[0m\n\u001b[32m[2025-07-10 18:46:53.973] [INFO] [GEN] [T:134830] Advanced systems initialized (logging, config, memory)\u001b[0m\nStarting ion implantation simulation...\nDEBUG: About to call simulateProcessAsync for doping\nDEBUG: simulateProcessAsync called for wafer: main_wafer, operation: doping\nDEBUG: simulateProcessAsync returned, getting result...\nDEBUG: Inside async lambda, about to call executeProcess\n\u001b[32m[2025-07-10 18:46:53.973] [INFO] [SIM] [T:134830] [SimulationEngine] Starting process: doping on wafer: main_wafer (Memory usage: 0 bytes) (in executeProcess at simulation_engine.cpp:232)\u001b[0m\n\u001b[32m[2025-07-10 18:46:53.973] [INFO] [PHYS] [T:134830] [EnhancedDoping] Enhanced Doping Physics initialized (in EnhancedDopingPhysics at enhanced_doping.cpp:15)\u001b[0m\n\u001b[32m[2025-07-10 18:46:53.973] [INFO] [PHYS] [T:134830] [EnhancedDoping] Starting enhanced ion implantation: B, Energy: 50.000000 keV, Dose: 100000000000000.000000 cm⁻², Tilt: 7.000000° (in simulateIonImplantation at simulation_engine.cpp:556)\u001b[0m\nDEBUG: executeProcess returned: true\n\u001b[32m[2025-07-10 18:46:53.977] [INFO] [PHYS] [T:134830] [EnhancedDoping] Enhanced ion implantation completed: B, Range: 0.000027 μm, Peak: 1000000000000000000000.000000 cm⁻³ (in simulateIonImplantation at enhanced_doping.cpp:111)\u001b[0m\nDEBUG: Result obtained: true\nIon implantation completed: SUCCESS\n\u001b[32m[2025-07-10 18:46:53.977] [INFO] [PHYS] [T:134830] [EnhancedDoping] Enhanced ion implantation completed: Range: 0.000027 μm, Straggling: 0.000011 μm, Peak conc: 1000000000000000000000.000000 cm⁻³, Sheet R: 1397.484833 Ω/sq, Junction depth: 0.000083 μm, Quality score: 90.201224 (in simulateIonImplantation at simulation_engine.cpp:575)\u001b[0m\n\u001b[32m[2025-07-10 18:46:53.977] [INFO] [SIM] [T:134830] Process completed successfully: doping (in executeProcess at simulation_engine.cpp:277)\u001b[0m\n\u001b[32m[2025-07-10 18:46:53.977] [INFO] [MEM] [T:134830] [MemoryManager] Starting memory cleanup (in cleanup at memory_manager.cpp:235)\u001b[0m\n\u001b[32m[2025-07-10 18:46:53.977] [INFO] [MEM] [T:134830] [MemoryManager] Memory cleanup complete (in cleanup at memory_manager.cpp:247)\u001b[0m\n\u001b[32m[2025-07-10 18:46:53.977] [INFO] [MEM] [T:134830] [MemoryManager] Memory Manager destroyed (in ~MemoryManager at memory_manager.cpp:28)\u001b[0m\n", "stderr": "", "output_files": 3}, "deposition": {"success": true, "execution_time": 0.0036509037017822266, "stdout": "SemiPRO C++ Simulator starting...\n\u001b[32m[2025-07-10 18:46:53.980] [INFO] [GEN] [T:126103] [ConfigManager] Configuration Manager initialized (in ConfigManager at config_manager.cpp:257)\u001b[0m\n\u001b[32m[2025-07-10 18:46:53.980] [INFO] [MEM] [T:126103] [MemoryManager] Enhanced Memory Manager initialized (in MemoryManager at memory_manager.cpp:13)\u001b[0m\n\u001b[32m[2025-07-10 18:46:53.980] [INFO] [GEN] [T:126103] Advanced systems initialized (logging, config, memory)\u001b[0m\nStarting deposition simulation...\n  Material: aluminum\n  Thickness: 0.1 μm\n  Temperature: 300°C\nDEBUG: About to call simulateProcessAsync for deposition\nDEBUG: simulateProcessAsync called for wafer: main_wafer, operation: deposition\nDEBUG: simulateProcessAsync returned, getting result...\nDEBUG: Inside async lambda, about to call executeProcess\n\u001b[32m[2025-07-10 18:46:53.980] [INFO] [SIM] [T:126103] [SimulationEngine] Starting process: deposition on wafer: main_wafer (Memory usage: 0 bytes) (in executeProcess at simulation_engine.cpp:232)\u001b[0m\n\u001b[32m[2025-07-10 18:46:53.980] [INFO] [PHYS] [T:126103] [EnhancedDeposition] Enhanced Deposition Physics initialized (in EnhancedDepositionPhysics at enhanced_deposition.cpp:15)\u001b[0m\n\u001b[32m[2025-07-10 18:46:53.980] [INFO] [PHYS] [T:126103] [EnhancedDeposition] Starting enhanced deposition: Aluminum, Technique: CVD, Thickness: 0.100000 μm, Temperature: 300.000000°C (in simulateDeposition at simulation_engine.cpp:668)\u001b[0m\n\u001b[32m[2025-07-10 18:46:53.981] [INFO] [PHYS] [T:126103] [EnhancedDeposition] Enhanced deposition completed: 0.100000 μm Aluminum deposited (in simulateDeposition at enhanced_deposition.cpp:108)\u001b[0m\nDEBUG: executeProcess returned: true\n\u001b[32m[2025-07-10 18:46:53.981] [INFO] [PHYS] [T:126103] [EnhancedDeposition] Enhanced deposition completed: Thickness: 0.100000 μm, Rate: 0.001098 μm/min, Step coverage: 60.000000%, Conformality: 60.000000%, Uniformity: 95.013939%, Quality score: 71.671313 (in simulateDeposition at simulation_engine.cpp:687)\u001b[0m\n\u001b[32m[2025-07-10 18:46:53.981] [INFO] [SIM] [T:126103] Process completed successfully: deposition (in executeProcess at simulation_engine.cpp:277)\u001b[0m\nDEBUG: Result obtained: true\nDeposition completed: SUCCESS\n\u001b[32m[2025-07-10 18:46:53.981] [INFO] [MEM] [T:126103] [MemoryManager] Starting memory cleanup (in cleanup at memory_manager.cpp:235)\u001b[0m\n\u001b[32m[2025-07-10 18:46:53.981] [INFO] [MEM] [T:126103] [MemoryManager] Memory cleanup complete (in cleanup at memory_manager.cpp:247)\u001b[0m\n\u001b[32m[2025-07-10 18:46:53.981] [INFO] [MEM] [T:126103] [MemoryManager] Memory Manager destroyed (in ~MemoryManager at memory_manager.cpp:28)\u001b[0m\n", "stderr": "", "output_files": 3}, "etching": {"success": true, "execution_time": 0.004016399383544922, "stdout": "SemiPRO C++ Simulator starting...\n\u001b[32m[2025-07-10 18:46:53.984] [INFO] [GEN] [T:127947] [ConfigManager] Configuration Manager initialized (in ConfigManager at config_manager.cpp:257)\u001b[0m\n\u001b[32m[2025-07-10 18:46:53.984] [INFO] [MEM] [T:127947] [MemoryManager] Enhanced Memory Manager initialized (in MemoryManager at memory_manager.cpp:13)\u001b[0m\n\u001b[32m[2025-07-10 18:46:53.984] [INFO] [GEN] [T:127947] Advanced systems initialized (logging, config, memory)\u001b[0m\nDEBUG: simulateProcessAsync called for wafer: main_wafer, operation: etching\nDEBUG: Inside async lambda, about to call executeProcess\n\u001b[32m[2025-07-10 18:46:53.984] [INFO] [SIM] [T:127947] [SimulationEngine] Starting process: etching on wafer: main_wafer (Memory usage: 0 bytes) (in executeProcess at simulation_engine.cpp:232)\u001b[0m\n\u001b[32m[2025-07-10 18:46:53.984] [INFO] [PHYS] [T:127947] [EnhancedEtching] Enhanced Etching Physics initialized (in EnhancedEtchingPhysics at enhanced_etching.cpp:15)\u001b[0m\n\u001b[32m[2025-07-10 18:46:53.984] [INFO] [PHYS] [T:127947] [EnhancedEtching] Starting enhanced etching: Silicon, Technique: RIE, Depth: 0.100000 μm, Pressure: 10.000000 mTorr (in simulateEtching at simulation_engine.cpp:786)\u001b[0m\n\u001b[32m[2025-07-10 18:46:53.985] [INFO] [PHYS] [T:127947] [EnhancedEtching] Enhanced etching completed: 0.100000 μm Silicon etched (in simulateEtching at enhanced_etching.cpp:114)\u001b[0m\nDEBUG: executeProcess returned: true\n\u001b[32m[2025-07-10 18:46:53.985] [INFO] [PHYS] [T:127947] [EnhancedEtching] Enhanced etching completed: Depth: 0.100000 μm, Rate: 0.531623 μm/min, Selectivity: 10.000000, Anisotropy: 96.000000%, Uniformity: 97.035018%, Quality score: 97.678339 (in simulateEtching at simulation_engine.cpp:805)\u001b[0m\n\u001b[32m[2025-07-10 18:46:53.985] [INFO] [SIM] [T:127947] Process completed successfully: etching (in executeProcess at simulation_engine.cpp:277)\u001b[0m\n\u001b[32m[2025-07-10 18:46:53.986] [INFO] [MEM] [T:127947] [MemoryManager] Starting memory cleanup (in cleanup at memory_manager.cpp:235)\u001b[0m\n\u001b[32m[2025-07-10 18:46:53.986] [INFO] [MEM] [T:127947] [MemoryManager] Memory cleanup complete (in cleanup at memory_manager.cpp:247)\u001b[0m\n\u001b[32m[2025-07-10 18:46:53.986] [INFO] [MEM] [T:127947] [MemoryManager] Memory Manager destroyed (in ~MemoryManager at memory_manager.cpp:28)\u001b[0m\n", "stderr": "", "output_files": 3}}, "analysis": {"total": 4, "successful": 4, "success_rate": 1.0, "working_modules": ["oxidation", "doping", "deposition", "etching"], "timeout_modules": [], "config_error_modules": []}}