{"timestamp": "2025-07-10 17:05:09", "results": {"oxidation": {"success": true, "execution_time": 0.0026879310607910156, "stdout": "SemiPRO C++ Simulator starting...\n\u001b[32m[2025-07-10 17:05:09.591] [INFO] [GEN] [T:139501] Advanced logging system initialized\u001b[0m\nDEBUG: simulateProcessAsync called for wafer: main_wafer, operation: oxidation\nDEBUG: Inside async lambda, about to call executeProcess\n\u001b[32m[2025-07-10 17:05:09.591] [INFO] [SIM] [T:139501] [SimulationEngine] Starting process: oxidation on wafer: main_wafer (in executeProcess at simulation_engine.cpp:217)\u001b[0m\nDEBUG: executeProcess returned: true\n\u001b[32m[2025-07-10 17:05:09.591] [INFO] [SIM] [T:139501] Process completed successfully: oxidation (in executeProcess at simulation_engine.cpp:261)\u001b[0m\n", "stderr": "", "output_files": 3}, "doping": {"success": true, "execution_time": 0.0026438236236572266, "stdout": "SemiPRO C++ Simulator starting...\n\u001b[32m[2025-07-10 17:05:09.594] [INFO] [GEN] [T:137394] Advanced logging system initialized\u001b[0m\nStarting ion implantation simulation...\nDEBUG: About to call simulateProcessAsync for doping\nDEBUG: simulateProcessAsync called for wafer: main_wafer, operation: doping\nDEBUG: simulateProcessAsync returned, getting result...\nDEBUG: Inside async lambda, about to call executeProcess\nDEBUG: simulateIonImplantation function entered\nDEBUG: Inside try block, extracting parameters\n\u001b[32m[2025-07-10 17:05:09.594] [INFO] [SIM] [T:137394] [SimulationEngine] Starting process: doping on wafer: main_wafer (in executeProcess at simulation_engine.cpp:217)\u001b[0m\nDEBUG: Ion implantation calculations:\n  energy=50 keV\n  dose=1e+14 cm^-2\n  mass=11 amu\n  atomic_number=5\n  epsilon=2.34282\n  reduced_range=0.07462\n  range=2.66259e-05 μm\n  straggling=1.11829e-05 μm\nDEBUG: executeProcess returned: true\n\u001b[32m[2025-07-10 17:05:09.595] [INFO] [SIM] [T:137394] Process completed successfully: doping (in executeProcess at simulation_engine.cpp:261)\u001b[0m\nDEBUG: Result obtained: true\nIon implantation completed: SUCCESS\n", "stderr": "", "output_files": 3}, "deposition": {"success": true, "execution_time": 0.0029451847076416016, "stdout": "SemiPRO C++ Simulator starting...\n\u001b[32m[2025-07-10 17:05:09.597] [INFO] [GEN] [T:140085] Advanced logging system initialized\u001b[0m\nStarting deposition simulation...\n  Material: aluminum\n  Thickness: 0.1 μm\n  Temperature: 300°C\nDEBUG: About to call simulateProcessAsync for deposition\nDEBUG: simulateProcessAsync called for wafer: main_wafer, operation: deposition\nDEBUG: simulateProcessAsync returned, getting result...\nDEBUG: Inside async lambda, about to call executeProcess\nDEBUG: Deposition calculations:\n  material=aluminum\n  thickness=0.1 μm\n  temperature=300°C\n  rate=0.1 μm/min\n  temp_factor=0.222393\n  effective_rate=0.0222393 μm/min\n  deposition_time=4.49654 minutes\n\u001b[32m[2025-07-10 17:05:09.598] [INFO] [SIM] [T:140085] [SimulationEngine] Starting process: deposition on wafer: main_wafer (in executeProcess at simulation_engine.cpp:217)\u001b[0m\nDEBUG: executeProcess returned: true\n\u001b[32m[2025-07-10 17:05:09.598] [INFO] [SIM] [T:140085] Process completed successfully: deposition (in executeProcess at simulation_engine.cpp:261)\u001b[0m\nDEBUG: Result obtained: true\nDeposition completed: SUCCESS\n", "stderr": "", "output_files": 3}, "etching": {"success": true, "execution_time": 0.0027179718017578125, "stdout": "SemiPRO C++ Simulator starting...\n\u001b[32m[2025-07-10 17:05:09.601] [INFO] [GEN] [T:130261] Advanced logging system initialized\u001b[0m\nDEBUG: simulateProcessAsync called for wafer: main_wafer, operation: etching\nDEBUG: Inside async lambda, about to call executeProcess\n\u001b[32m[2025-07-10 17:05:09.601] [INFO] [SIM] [T:130261] [SimulationEngine] Starting process: etching on wafer: main_wafer (in executeProcess at simulation_engine.cpp:217)\u001b[0m\nDEBUG: executeProcess returned: true\n\u001b[32m[2025-07-10 17:05:09.601] [INFO] [SIM] [T:130261] Process completed successfully: etching (in executeProcess at simulation_engine.cpp:261)\u001b[0m\n", "stderr": "", "output_files": 3}}, "analysis": {"total": 4, "successful": 4, "success_rate": 1.0, "working_modules": ["oxidation", "doping", "deposition", "etching"], "timeout_modules": [], "config_error_modules": []}}