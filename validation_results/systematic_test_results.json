{"timestamp": "2025-07-10 17:25:41", "results": {"oxidation": {"success": true, "execution_time": 0.003085613250732422, "stdout": "SemiPRO C++ Simulator starting...\n\u001b[32m[2025-07-10 17:25:41.143] [INFO] [GEN] [T:138569] [ConfigManager] Configuration Manager initialized (in ConfigManager at config_manager.cpp:257)\u001b[0m\n\u001b[32m[2025-07-10 17:25:41.143] [INFO] [MEM] [T:138569] [MemoryManager] Enhanced Memory Manager initialized (in MemoryManager at memory_manager.cpp:13)\u001b[0m\n\u001b[32m[2025-07-10 17:25:41.143] [INFO] [GEN] [T:138569] Advanced systems initialized (logging, config, memory)\u001b[0m\nDEBUG: simulateProcessAsync called for wafer: main_wafer, operation: oxidation\nDEBUG: Inside async lambda, about to call executeProcess\n\u001b[32m[2025-07-10 17:25:41.143] [INFO] [SIM] [T:138569] [SimulationEngine] Starting process: oxidation on wafer: main_wafer (Memory usage: 0 bytes) (in executeProcess at simulation_engine.cpp:228)\u001b[0m\nDEBUG: executeProcess returned: true\n\u001b[32m[2025-07-10 17:25:41.143] [INFO] [SIM] [T:138569] Process completed successfully: oxidation (in executeProcess at simulation_engine.cpp:273)\u001b[0m\n\u001b[32m[2025-07-10 17:25:41.144] [INFO] [MEM] [T:138569] [MemoryManager] Starting memory cleanup (in cleanup at memory_manager.cpp:235)\u001b[0m\n\u001b[32m[2025-07-10 17:25:41.144] [INFO] [MEM] [T:138569] [MemoryManager] Memory cleanup complete (in cleanup at memory_manager.cpp:247)\u001b[0m\n\u001b[32m[2025-07-10 17:25:41.144] [INFO] [MEM] [T:138569] [MemoryManager] Memory Manager destroyed (in ~MemoryManager at memory_manager.cpp:28)\u001b[0m\n", "stderr": "", "output_files": 3}, "doping": {"success": true, "execution_time": 0.003226041793823242, "stdout": "SemiPRO C++ Simulator starting...\n\u001b[32m[2025-07-10 17:25:41.147] [INFO] [GEN] [T:136486] [ConfigManager] Configuration Manager initialized (in ConfigManager at config_manager.cpp:257)\u001b[0m\n\u001b[32m[2025-07-10 17:25:41.147] [INFO] [MEM] [T:136486] [MemoryManager] Enhanced Memory Manager initialized (in MemoryManager at memory_manager.cpp:13)\u001b[0m\n\u001b[32m[2025-07-10 17:25:41.147] [INFO] [GEN] [T:136486] Advanced systems initialized (logging, config, memory)\u001b[0m\nStarting ion implantation simulation...\nDEBUG: About to call simulateProcessAsync for doping\nDEBUG: simulateProcessAsync called for wafer: main_wafer, operation: doping\nDEBUG: simulateProcessAsync returned, getting result...\nDEBUG: Inside async lambda, about to call executeProcess\nDEBUG: simulateIonImplantation function entered\nDEBUG: Inside try block, extracting parameters\nDEBUG: Ion implantation calculations:\n  energy=50 keV\n  dose=1e+14 cm^-2\n  mass=11 amu\n  atomic_number=5\n  epsilon=2.34282\n  reduced_range=0.07462\n  range=2.66259e-05 μm\n  straggling=1.11829e-05 μm\n\u001b[32m[2025-07-10 17:25:41.147] [INFO] [SIM] [T:136486] [SimulationEngine] Starting process: doping on wafer: main_wafer (Memory usage: 0 bytes) (in executeProcess at simulation_engine.cpp:228)\u001b[0m\nDEBUG: executeProcess returned: true\n\u001b[32m[2025-07-10 17:25:41.148] [INFO] [SIM] [T:136486] Process completed successfully: doping (in executeProcess at simulation_engine.cpp:273)\u001b[0m\nDEBUG: Result obtained: true\nIon implantation completed: SUCCESS\n\u001b[32m[2025-07-10 17:25:41.148] [INFO] [MEM] [T:136486] [MemoryManager] Starting memory cleanup (in cleanup at memory_manager.cpp:235)\u001b[0m\n\u001b[32m[2025-07-10 17:25:41.148] [INFO] [MEM] [T:136486] [MemoryManager] Memory cleanup complete (in cleanup at memory_manager.cpp:247)\u001b[0m\n\u001b[32m[2025-07-10 17:25:41.148] [INFO] [MEM] [T:136486] [MemoryManager] Memory Manager destroyed (in ~MemoryManager at memory_manager.cpp:28)\u001b[0m\n", "stderr": "", "output_files": 3}, "deposition": {"success": true, "execution_time": 0.0032432079315185547, "stdout": "SemiPRO C++ Simulator starting...\n\u001b[32m[2025-07-10 17:25:41.150] [INFO] [GEN] [T:127453] [ConfigManager] Configuration Manager initialized (in ConfigManager at config_manager.cpp:257)\u001b[0m\n\u001b[32m[2025-07-10 17:25:41.150] [INFO] [MEM] [T:127453] [MemoryManager] Enhanced Memory Manager initialized (in MemoryManager at memory_manager.cpp:13)\u001b[0m\n\u001b[32m[2025-07-10 17:25:41.150] [INFO] [GEN] [T:127453] Advanced systems initialized (logging, config, memory)\u001b[0m\nStarting deposition simulation...\n  Material: aluminum\n  Thickness: 0.1 μm\n  Temperature: 300°C\nDEBUG: About to call simulateProcessAsync for deposition\nDEBUG: simulateProcessAsync called for wafer: main_wafer, operation: deposition\nDEBUG: simulateProcessAsync returned, getting result...\nDEBUG: Inside async lambda, about to call executeProcess\nDEBUG: Deposition calculations:\n  material=aluminum\n  thickness=0.1 μm\n  temperature=300°C\n  rate=0.1 μm/min\n  temp_factor=0.222393\n  effective_rate=0.0222393 μm/min\n  deposition_time=4.49654 minutes\n\u001b[32m[2025-07-10 17:25:41.151] [INFO] [SIM] [T:127453] [SimulationEngine] Starting process: deposition on wafer: main_wafer (Memory usage: 0 bytes) (in executeProcess at simulation_engine.cpp:228)\u001b[0m\nDEBUG: executeProcess returned: true\nDEBUG: Result obtained: true\nDeposition completed: SUCCESS\n\u001b[32m[2025-07-10 17:25:41.151] [INFO] [SIM] [T:127453] Process completed successfully: deposition (in executeProcess at simulation_engine.cpp:273)\u001b[0m\n\u001b[32m[2025-07-10 17:25:41.151] [INFO] [MEM] [T:127453] [MemoryManager] Starting memory cleanup (in cleanup at memory_manager.cpp:235)\u001b[0m\n\u001b[32m[2025-07-10 17:25:41.151] [INFO] [MEM] [T:127453] [MemoryManager] Memory cleanup complete (in cleanup at memory_manager.cpp:247)\u001b[0m\n\u001b[32m[2025-07-10 17:25:41.151] [INFO] [MEM] [T:127453] [MemoryManager] Memory Manager destroyed (in ~MemoryManager at memory_manager.cpp:28)\u001b[0m\n", "stderr": "", "output_files": 3}, "etching": {"success": true, "execution_time": 0.0032203197479248047, "stdout": "SemiPRO C++ Simulator starting...\n\u001b[32m[2025-07-10 17:25:41.154] [INFO] [GEN] [T:137536] [ConfigManager] Configuration Manager initialized (in ConfigManager at config_manager.cpp:257)\u001b[0m\n\u001b[32m[2025-07-10 17:25:41.155] [INFO] [MEM] [T:137536] [MemoryManager] Enhanced Memory Manager initialized (in MemoryManager at memory_manager.cpp:13)\u001b[0m\n\u001b[32m[2025-07-10 17:25:41.155] [INFO] [GEN] [T:137536] Advanced systems initialized (logging, config, memory)\u001b[0m\nDEBUG: simulateProcessAsync called for wafer: main_wafer, operation: etching\nDEBUG: Inside async lambda, about to call executeProcess\n\u001b[32m[2025-07-10 17:25:41.155] [INFO] [SIM] [T:137536] [SimulationEngine] Starting process: etching on wafer: main_wafer (Memory usage: 0 bytes) (in executeProcess at simulation_engine.cpp:228)\u001b[0m\nDEBUG: executeProcess returned: true\n\u001b[32m[2025-07-10 17:25:41.155] [INFO] [SIM] [T:137536] Process completed successfully: etching (in executeProcess at simulation_engine.cpp:273)\u001b[0m\n\u001b[32m[2025-07-10 17:25:41.155] [INFO] [MEM] [T:137536] [MemoryManager] Starting memory cleanup (in cleanup at memory_manager.cpp:235)\u001b[0m\n\u001b[32m[2025-07-10 17:25:41.155] [INFO] [MEM] [T:137536] [MemoryManager] Memory cleanup complete (in cleanup at memory_manager.cpp:247)\u001b[0m\n\u001b[32m[2025-07-10 17:25:41.155] [INFO] [MEM] [T:137536] [MemoryManager] Memory Manager destroyed (in ~MemoryManager at memory_manager.cpp:28)\u001b[0m\n", "stderr": "", "output_files": 3}}, "analysis": {"total": 4, "successful": 4, "success_rate": 1.0, "working_modules": ["oxidation", "doping", "deposition", "etching"], "timeout_modules": [], "config_error_modules": []}}