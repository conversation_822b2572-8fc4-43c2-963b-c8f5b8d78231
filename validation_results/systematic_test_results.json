{"timestamp": "2025-07-10 18:26:28", "results": {"oxidation": {"success": true, "execution_time": 0.0034067630767822266, "stdout": "SemiPRO C++ Simulator starting...\n\u001b[32m[2025-07-10 18:26:28.536] [INFO] [GEN] [T:124824] [ConfigManager] Configuration Manager initialized (in ConfigManager at config_manager.cpp:257)\u001b[0m\n\u001b[32m[2025-07-10 18:26:28.536] [INFO] [MEM] [T:124824] [MemoryManager] Enhanced Memory Manager initialized (in MemoryManager at memory_manager.cpp:13)\u001b[0m\n\u001b[32m[2025-07-10 18:26:28.536] [INFO] [GEN] [T:124824] Advanced systems initialized (logging, config, memory)\u001b[0m\nDEBUG: simulateProcessAsync called for wafer: main_wafer, operation: oxidation\nDEBUG: Inside async lambda, about to call executeProcess\n\u001b[32m[2025-07-10 18:26:28.536] [INFO] [SIM] [T:124824] [SimulationEngine] Starting process: oxidation on wafer: main_wafer (Memory usage: 0 bytes) (in executeProcess at simulation_engine.cpp:231)\u001b[0m\n\u001b[32m[2025-07-10 18:26:28.536] [INFO] [PHYS] [T:124824] [EnhancedOxidation] Enhanced Oxidation Physics initialized (in EnhancedOxidationPhysics at enhanced_oxidation.cpp:13)\u001b[0m\n\u001b[32m[2025-07-10 18:26:28.536] [INFO] [PHYS] [T:124824] [EnhancedOxidation] Starting enhanced oxidation: 1000.000000°C, 1.000000h, Dry O2 (in simulateOxidation at simulation_engine.cpp:448)\u001b[0m\nDEBUG: executeProcess returned: true\n\u001b[32m[2025-07-10 18:26:28.537] [INFO] [PHYS] [T:124824] [EnhancedOxidation] Enhanced oxidation completed: 0.557734 μm oxide grown (in simulateOxidation at enhanced_oxidation.cpp:86)\u001b[0m\n\u001b[32m[2025-07-10 18:26:28.537] [INFO] [PHYS] [T:124824] [EnhancedOxidation] Enhanced oxidation completed: 0.557734 μm oxide grown, Growth rate: 0.009137 μm/h, Regime: Thick oxide, Stress: 96482.665162 MPa, Quality score: 0.000000 (in simulateOxidation at simulation_engine.cpp:458)\u001b[0m\n\u001b[32m[2025-07-10 18:26:28.537] [INFO] [SIM] [T:124824] Process completed successfully: oxidation (in executeProcess at simulation_engine.cpp:276)\u001b[0m\n\u001b[32m[2025-07-10 18:26:28.537] [INFO] [MEM] [T:124824] [MemoryManager] Starting memory cleanup (in cleanup at memory_manager.cpp:235)\u001b[0m\n\u001b[32m[2025-07-10 18:26:28.537] [INFO] [MEM] [T:124824] [MemoryManager] Memory cleanup complete (in cleanup at memory_manager.cpp:247)\u001b[0m\n\u001b[32m[2025-07-10 18:26:28.537] [INFO] [MEM] [T:124824] [MemoryManager] Memory Manager destroyed (in ~MemoryManager at memory_manager.cpp:28)\u001b[0m\n", "stderr": "", "output_files": 3}, "doping": {"success": true, "execution_time": 0.006909370422363281, "stdout": "SemiPRO C++ Simulator starting...\n\u001b[32m[2025-07-10 18:26:28.540] [INFO] [GEN] [T:123552] [ConfigManager] Configuration Manager initialized (in ConfigManager at config_manager.cpp:257)\u001b[0m\n\u001b[32m[2025-07-10 18:26:28.540] [INFO] [MEM] [T:123552] [MemoryManager] Enhanced Memory Manager initialized (in MemoryManager at memory_manager.cpp:13)\u001b[0m\n\u001b[32m[2025-07-10 18:26:28.540] [INFO] [GEN] [T:123552] Advanced systems initialized (logging, config, memory)\u001b[0m\nStarting ion implantation simulation...\nDEBUG: About to call simulateProcessAsync for doping\nDEBUG: simulateProcessAsync called for wafer: main_wafer, operation: doping\nDEBUG: simulateProcessAsync returned, getting result...\nDEBUG: Inside async lambda, about to call executeProcess\n\u001b[32m[2025-07-10 18:26:28.541] [INFO] [SIM] [T:123552] [SimulationEngine] Starting process: doping on wafer: main_wafer (Memory usage: 0 bytes) (in executeProcess at simulation_engine.cpp:231)\u001b[0m\n\u001b[32m[2025-07-10 18:26:28.541] [INFO] [PHYS] [T:123552] [EnhancedDoping] Enhanced Doping Physics initialized (in EnhancedDopingPhysics at enhanced_doping.cpp:15)\u001b[0m\n\u001b[32m[2025-07-10 18:26:28.541] [INFO] [PHYS] [T:123552] [EnhancedDoping] Starting enhanced ion implantation: B, Energy: 50.000000 keV, Dose: 100000000000000.000000 cm⁻², Tilt: 7.000000° (in simulateIonImplantation at simulation_engine.cpp:555)\u001b[0m\nDEBUG: executeProcess returned: true\n\u001b[32m[2025-07-10 18:26:28.544] [INFO] [PHYS] [T:123552] [EnhancedDoping] Enhanced ion implantation completed: B, Range: 0.000027 μm, Peak: 1000000000000000000000.000000 cm⁻³ (in simulateIonImplantation at enhanced_doping.cpp:111)\u001b[0m\n\u001b[32m[2025-07-10 18:26:28.544] [INFO] [PHYS] [T:123552] [EnhancedDoping] Enhanced ion implantation completed: Range: 0.000027 μm, Straggling: 0.000011 μm, Peak conc: 1000000000000000000000.000000 cm⁻³, Sheet R: 1397.484833 Ω/sq, Junction depth: 0.000083 μm, Quality score: 90.201224 (in simulateIonImplantation at simulation_engine.cpp:574)\u001b[0m\nDEBUG: Result obtained: true\nIon implantation completed: SUCCESS\n\u001b[32m[2025-07-10 18:26:28.544] [INFO] [SIM] [T:123552] Process completed successfully: doping (in executeProcess at simulation_engine.cpp:276)\u001b[0m\n\u001b[32m[2025-07-10 18:26:28.545] [INFO] [MEM] [T:123552] [MemoryManager] Starting memory cleanup (in cleanup at memory_manager.cpp:235)\u001b[0m\n\u001b[32m[2025-07-10 18:26:28.545] [INFO] [MEM] [T:123552] [MemoryManager] Memory cleanup complete (in cleanup at memory_manager.cpp:247)\u001b[0m\n\u001b[32m[2025-07-10 18:26:28.545] [INFO] [MEM] [T:123552] [MemoryManager] Memory Manager destroyed (in ~MemoryManager at memory_manager.cpp:28)\u001b[0m\n", "stderr": "", "output_files": 3}, "deposition": {"success": true, "execution_time": 0.003523111343383789, "stdout": "SemiPRO C++ Simulator starting...\n\u001b[32m[2025-07-10 18:26:28.547] [INFO] [GEN] [T:127649] [ConfigManager] Configuration Manager initialized (in ConfigManager at config_manager.cpp:257)\u001b[0m\n\u001b[32m[2025-07-10 18:26:28.548] [INFO] [MEM] [T:127649] [MemoryManager] Enhanced Memory Manager initialized (in MemoryManager at memory_manager.cpp:13)\u001b[0m\n\u001b[32m[2025-07-10 18:26:28.548] [INFO] [GEN] [T:127649] Advanced systems initialized (logging, config, memory)\u001b[0m\nStarting deposition simulation...\n  Material: aluminum\n  Thickness: 0.1 μm\n  Temperature: 300°C\nDEBUG: About to call simulateProcessAsync for deposition\nDEBUG: simulateProcessAsync called for wafer: main_wafer, operation: deposition\nDEBUG: simulateProcessAsync returned, getting result...\nDEBUG: Inside async lambda, about to call executeProcess\n\u001b[32m[2025-07-10 18:26:28.548] [INFO] [SIM] [T:127649] [SimulationEngine] Starting process: deposition on wafer: main_wafer (Memory usage: 0 bytes) (in executeProcess at simulation_engine.cpp:231)\u001b[0m\n\u001b[32m[2025-07-10 18:26:28.548] [INFO] [PHYS] [T:127649] [EnhancedDeposition] Enhanced Deposition Physics initialized (in EnhancedDepositionPhysics at enhanced_deposition.cpp:15)\u001b[0m\n\u001b[32m[2025-07-10 18:26:28.548] [INFO] [PHYS] [T:127649] [EnhancedDeposition] Starting enhanced deposition: Aluminum, Technique: CVD, Thickness: 0.100000 μm, Temperature: 300.000000°C (in simulateDeposition at simulation_engine.cpp:667)\u001b[0m\nDEBUG: executeProcess returned: true\n\u001b[32m[2025-07-10 18:26:28.548] [INFO] [PHYS] [T:127649] [EnhancedDeposition] Enhanced deposition completed: 0.100000 μm Aluminum deposited (in simulateDeposition at enhanced_deposition.cpp:108)\u001b[0m\nDEBUG: Result obtained: true\nDeposition completed: SUCCESS\n\u001b[32m[2025-07-10 18:26:28.548] [INFO] [PHYS] [T:127649] [EnhancedDeposition] Enhanced deposition completed: Thickness: 0.100000 μm, Rate: 0.001098 μm/min, Step coverage: 60.000000%, Conformality: 60.000000%, Uniformity: 95.108985%, Quality score: 71.702995 (in simulateDeposition at simulation_engine.cpp:686)\u001b[0m\n\u001b[32m[2025-07-10 18:26:28.548] [INFO] [SIM] [T:127649] Process completed successfully: deposition (in executeProcess at simulation_engine.cpp:276)\u001b[0m\n\u001b[32m[2025-07-10 18:26:28.549] [INFO] [MEM] [T:127649] [MemoryManager] Starting memory cleanup (in cleanup at memory_manager.cpp:235)\u001b[0m\n\u001b[32m[2025-07-10 18:26:28.549] [INFO] [MEM] [T:127649] [MemoryManager] Memory cleanup complete (in cleanup at memory_manager.cpp:247)\u001b[0m\n\u001b[32m[2025-07-10 18:26:28.549] [INFO] [MEM] [T:127649] [MemoryManager] Memory Manager destroyed (in ~MemoryManager at memory_manager.cpp:28)\u001b[0m\n", "stderr": "", "output_files": 3}, "etching": {"success": true, "execution_time": 0.0031223297119140625, "stdout": "SemiPRO C++ Simulator starting...\n\u001b[32m[2025-07-10 18:26:28.551] [INFO] [GEN] [T:129221] [ConfigManager] Configuration Manager initialized (in ConfigManager at config_manager.cpp:257)\u001b[0m\n\u001b[32m[2025-07-10 18:26:28.551] [INFO] [MEM] [T:129221] [MemoryManager] Enhanced Memory Manager initialized (in MemoryManager at memory_manager.cpp:13)\u001b[0m\n\u001b[32m[2025-07-10 18:26:28.551] [INFO] [GEN] [T:129221] Advanced systems initialized (logging, config, memory)\u001b[0m\nDEBUG: simulateProcessAsync called for wafer: main_wafer, operation: etching\nDEBUG: Inside async lambda, about to call executeProcess\n\u001b[32m[2025-07-10 18:26:28.552] [INFO] [SIM] [T:129221] [SimulationEngine] Starting process: etching on wafer: main_wafer (Memory usage: 0 bytes) (in executeProcess at simulation_engine.cpp:231)\u001b[0m\nDEBUG: executeProcess returned: true\n\u001b[32m[2025-07-10 18:26:28.552] [INFO] [SIM] [T:129221] Process completed successfully: etching (in executeProcess at simulation_engine.cpp:276)\u001b[0m\n\u001b[32m[2025-07-10 18:26:28.552] [INFO] [MEM] [T:129221] [MemoryManager] Starting memory cleanup (in cleanup at memory_manager.cpp:235)\u001b[0m\n\u001b[32m[2025-07-10 18:26:28.552] [INFO] [MEM] [T:129221] [MemoryManager] Memory cleanup complete (in cleanup at memory_manager.cpp:247)\u001b[0m\n\u001b[32m[2025-07-10 18:26:28.552] [INFO] [MEM] [T:129221] [MemoryManager] Memory Manager destroyed (in ~MemoryManager at memory_manager.cpp:28)\u001b[0m\n", "stderr": "", "output_files": 3}}, "analysis": {"total": 4, "successful": 4, "success_rate": 1.0, "working_modules": ["oxidation", "doping", "deposition", "etching"], "timeout_modules": [], "config_error_modules": []}}